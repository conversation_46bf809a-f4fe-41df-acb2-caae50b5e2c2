#!/usr/bin/env python3
"""
测试排除功能的效果
验证排除最近50期模式和数量的功能
"""
import sys
import os
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager
from adaptive_prediction_system import AdaptivePredictionSystem


def analyze_exclusion_effectiveness():
    """分析排除功能的有效性"""
    print("=" * 80)
    print("排除功能效果分析")
    print("=" * 80)
    
    try:
        # 加载数据
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        data = df.to_dict('records')
        
        if len(data) < 100:
            print("数据不足")
            return
        
        # 创建预测系统
        predictor = AdaptivePredictionSystem()
        
        # 分析最近50期的模式和数量分布
        recent_50 = data[-50:]
        
        print(f"最近50期数据分析:")
        print(f"期号范围: {recent_50[0]['期号']} - {recent_50[-1]['期号']}")
        
        # 统计模式和数量
        patterns = []
        counts = []
        
        for record in recent_50:
            zone_dist = predictor.parse_zone_ratio(record['分区比'])
            zero_pattern = predictor.get_zero_pattern(zone_dist)
            zero_count = zero_pattern.count('0')
            
            patterns.append(zero_pattern)
            counts.append(zero_count)
        
        # 模式分析
        pattern_counter = Counter(patterns)
        unique_patterns = len(pattern_counter)
        total_patterns = len(patterns)
        
        print(f"\n模式分析:")
        print(f"  总期数: {total_patterns}")
        print(f"  唯一模式数: {unique_patterns}")
        print(f"  重复率: {(total_patterns - unique_patterns) / total_patterns:.1%}")
        
        print(f"\n最常见的模式:")
        for pattern, count in pattern_counter.most_common(10):
            print(f"  {pattern}: {count}次")
        
        # 数量分析
        count_counter = Counter(counts)
        unique_counts = len(count_counter)
        
        print(f"\n数量分析:")
        print(f"  唯一数量种类: {unique_counts}")
        print(f"  数量分布:")
        for count in sorted(count_counter.keys()):
            freq = count_counter[count]
            pct = freq / total_patterns * 100
            print(f"    {count}个0分区: {freq}次 ({pct:.1f}%)")
        
        # 测试排除功能
        print(f"\n" + "=" * 60)
        print("排除功能测试")
        print("=" * 60)
        
        # 使用前70期数据预测最近20期
        train_data = data[:-20]
        test_data = data[-20:]
        
        exclusion_stats = {
            'patterns_excluded': [],
            'counts_excluded': [],
            'generation_attempts': [],
            'successful_exclusions': 0
        }
        
        print(f"测试设置:")
        print(f"  训练数据: {len(train_data)}期")
        print(f"  测试数据: {len(test_data)}期")
        print(f"  排除窗口: 50期")
        
        print(f"\n排除效果测试:")
        print("期号      实际模式    预测模式    排除数  尝试次数  是否新颖")
        print("-" * 70)
        
        for i, test_record in enumerate(test_data):
            # 获取历史数据
            history = train_data + test_data[:i]
            recent_history = history[-100:]
            
            # 获取排除信息
            exclude_patterns, exclude_counts = predictor.get_recent_patterns_and_counts(
                recent_history, exclude_periods=50
            )
            
            # 生成预测
            prediction = predictor.generate_adaptive_prediction(recent_history)
            predicted_pattern = prediction['predicted_pattern']
            
            # 实际结果
            actual_zone_dist = predictor.parse_zone_ratio(test_record['分区比'])
            actual_pattern = predictor.get_zero_pattern(actual_zone_dist)
            
            # 检查是否成功排除
            is_novel = predicted_pattern not in exclude_patterns
            attempts = prediction.get('generation_attempts', 1)
            excluded_count = len(exclude_patterns)
            
            # 统计
            exclusion_stats['patterns_excluded'].append(excluded_count)
            exclusion_stats['generation_attempts'].append(attempts)
            if is_novel:
                exclusion_stats['successful_exclusions'] += 1
            
            novelty_status = "✓新颖" if is_novel else "✗重复"
            
            print(f"{test_record['期号']:<8} {actual_pattern:<10} {predicted_pattern:<10} "
                  f"{excluded_count:<6} {attempts:<8} {novelty_status}")
        
        # 统计结果
        print(f"\n" + "=" * 60)
        print("排除功能统计结果")
        print("=" * 60)
        
        avg_excluded = np.mean(exclusion_stats['patterns_excluded'])
        avg_attempts = np.mean(exclusion_stats['generation_attempts'])
        success_rate = exclusion_stats['successful_exclusions'] / len(test_data)
        
        print(f"排除效果:")
        print(f"  平均排除模式数: {avg_excluded:.1f}个")
        print(f"  平均生成尝试次数: {avg_attempts:.1f}次")
        print(f"  新颖性成功率: {success_rate:.1%}")
        
        print(f"\n生成尝试分布:")
        attempt_counter = Counter(exclusion_stats['generation_attempts'])
        for attempts in sorted(attempt_counter.keys()):
            freq = attempt_counter[attempts]
            pct = freq / len(test_data) * 100
            print(f"  {attempts}次尝试: {freq}期 ({pct:.1f}%)")
        
        print(f"\n排除模式数分布:")
        excluded_counter = Counter(exclusion_stats['patterns_excluded'])
        for excluded in sorted(excluded_counter.keys()):
            freq = excluded_counter[excluded]
            pct = freq / len(test_data) * 100
            print(f"  排除{excluded}个模式: {freq}期 ({pct:.1f}%)")
        
        # 分析排除的价值
        print(f"\n" + "=" * 60)
        print("排除功能价值分析")
        print("=" * 60)
        
        print(f"功能优势:")
        print(f"  ✅ 确保预测模式新颖性: {success_rate:.1%}成功率")
        print(f"  ✅ 高效模式生成: 平均{avg_attempts:.1f}次尝试")
        print(f"  ✅ 大量历史排除: 平均排除{avg_excluded:.1f}个模式")
        print(f"  ✅ 避免简单重复: 提升预测价值")
        
        if avg_attempts <= 5:
            print(f"  🎯 生成效率优秀: 快速找到新颖模式")
        
        if success_rate >= 0.8:
            print(f"  🏆 排除效果卓越: 高新颖性保证")
        
        print(f"\n实际应用建议:")
        if avg_excluded >= 30:
            print(f"  📊 排除窗口合适: 50期能排除足够多的历史模式")
        
        if avg_attempts <= 10:
            print(f"  ⚡ 算法效率高: 快速生成不重复模式")
        
        print(f"  💡 用户收益: 获得更有价值的预测参考")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    analyze_exclusion_effectiveness()


if __name__ == "__main__":
    main()
