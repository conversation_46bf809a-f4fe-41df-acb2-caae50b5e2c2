#!/usr/bin/env python3
"""
高级模式分析：连续x模式和0分区数量转换规律
基于o3-mini模型指导，深度分析连续x模式对0分区数量的影响和转换规律
"""
import sys
import os
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict
import scipy.stats as stats

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager


class AdvancedPatternAnalyzer:
    """高级模式分析器"""
    
    def __init__(self):
        self.data = None
        self.pattern_data = []
        self.consecutive_x_cases = []  # 连续x案例
        self.zero_count_transitions = []  # 0分区数量转换案例
        
    def load_and_prepare_data(self):
        """加载并预处理数据"""
        print("=" * 80)
        print("高级模式分析：连续x模式和0分区数量转换规律")
        print("=" * 80)
        
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        self.data = df.to_dict('records')
        
        # 预处理数据
        for record in self.data:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            zero_pattern = self.get_zero_pattern(zone_dist)
            zero_count = zero_pattern.count('0')
            
            self.pattern_data.append({
                'period': record['期号'],
                'pattern': zero_pattern,
                'zero_count': zero_count,
                'date': record.get('日期', ''),
                'zone_dist': zone_dist
            })
        
        print(f"数据预处理完成: {len(self.data)} 条记录")
    
    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])
    
    def find_consecutive_x(self, pattern: str) -> List[Tuple[int, int]]:
        """找出模式中连续x的位置和长度"""
        consecutive_x = []
        i = 0
        while i < len(pattern):
            if pattern[i] == 'x':
                start = i
                while i < len(pattern) and pattern[i] == 'x':
                    i += 1
                length = i - start
                if length >= 3:  # 只关注连续3个或以上的x
                    consecutive_x.append((start, length))
            else:
                i += 1
        return consecutive_x
    
    def analyze_consecutive_x_patterns(self):
        """分析连续x模式"""
        print(f"\n" + "=" * 60)
        print("连续x模式识别与分析")
        print("=" * 60)
        
        # 识别连续x案例
        for i, pattern_info in enumerate(self.pattern_data):
            pattern = pattern_info['pattern']
            consecutive_x = self.find_consecutive_x(pattern)
            
            for start_pos, length in consecutive_x:
                case_info = {
                    'index': i,
                    'period': pattern_info['period'],
                    'pattern': pattern,
                    'zero_count': pattern_info['zero_count'],
                    'start_pos': start_pos,
                    'length': length,
                    'next_period_info': None
                }
                
                # 获取下一期信息
                if i + 1 < len(self.pattern_data):
                    next_info = self.pattern_data[i + 1]
                    case_info['next_period_info'] = {
                        'period': next_info['period'],
                        'pattern': next_info['pattern'],
                        'zero_count': next_info['zero_count']
                    }
                
                self.consecutive_x_cases.append(case_info)
        
        # 按长度分类统计
        x3_cases = [case for case in self.consecutive_x_cases if case['length'] == 3]
        x4_cases = [case for case in self.consecutive_x_cases if case['length'] == 4]
        x5_plus_cases = [case for case in self.consecutive_x_cases if case['length'] >= 5]
        
        print(f"连续3个x的案例: {len(x3_cases)}个")
        print(f"连续4个x的案例: {len(x4_cases)}个")
        print(f"连续5个以上x的案例: {len(x5_plus_cases)}个")
        
        # 分析连续x对下一期0分区数量的影响
        self.analyze_x_impact_on_zero_count(x3_cases, "连续3个x")
        self.analyze_x_impact_on_zero_count(x4_cases, "连续4个x")
        if x5_plus_cases:
            self.analyze_x_impact_on_zero_count(x5_plus_cases, "连续5个以上x")
    
    def analyze_x_impact_on_zero_count(self, cases: List[Dict], title: str):
        """分析连续x对下一期0分区数量的影响"""
        print(f"\n{title}对下一期0分区数量的影响:")
        
        if not cases:
            print("  没有相关案例")
            return
        
        # 收集下一期的0分区数量
        next_zero_counts = []
        for case in cases:
            if case['next_period_info']:
                next_zero_counts.append(case['next_period_info']['zero_count'])
        
        if not next_zero_counts:
            print("  没有有效的下一期数据")
            return
        
        # 统计分析
        count_distribution = Counter(next_zero_counts)
        mean_count = np.mean(next_zero_counts)
        
        print(f"  下一期0分区数量分布:")
        for count in sorted(count_distribution.keys()):
            freq = count_distribution[count]
            pct = freq / len(next_zero_counts) * 100
            print(f"    {count}个0分区: {freq}次 ({pct:.1f}%)")
        
        print(f"  平均0分区数量: {mean_count:.2f}")
        
        # 与整体分布比较
        all_zero_counts = [info['zero_count'] for info in self.pattern_data]
        overall_mean = np.mean(all_zero_counts)
        
        print(f"  整体平均: {overall_mean:.2f}")
        print(f"  差异: {mean_count - overall_mean:+.2f}")
        
        # 统计显著性检验
        if len(next_zero_counts) >= 10:
            t_stat, p_value = stats.ttest_1samp(next_zero_counts, overall_mean)
            print(f"  t检验 p值: {p_value:.4f}")
            if p_value < 0.05:
                print(f"  ✓ 与整体均值存在显著差异")
            else:
                print(f"  - 与整体均值无显著差异")
    
    def analyze_zero_count_transitions(self):
        """分析0分区数量转换规律"""
        print(f"\n" + "=" * 60)
        print("0分区数量转换规律分析")
        print("=" * 60)
        
        # 构建转换序列
        for i in range(len(self.pattern_data) - 1):
            current = self.pattern_data[i]
            next_period = self.pattern_data[i + 1]
            
            transition = {
                'from_period': current['period'],
                'to_period': next_period['period'],
                'from_count': current['zero_count'],
                'to_count': next_period['zero_count'],
                'from_pattern': current['pattern'],
                'to_pattern': next_period['pattern']
            }
            
            self.zero_count_transitions.append(transition)
        
        # 分析3个0和4个0的转换
        self.analyze_specific_transitions(3, 4, "3个0 → 4个0")
        self.analyze_specific_transitions(4, 3, "4个0 → 3个0")
        self.analyze_consecutive_stability(3, "3个0")
        self.analyze_consecutive_stability(4, "4个0")
        
        # 分析交替模式
        self.analyze_alternating_patterns()
    
    def analyze_specific_transitions(self, from_count: int, to_count: int, title: str):
        """分析特定的转换规律"""
        print(f"\n{title}转换分析:")
        
        # 找出所有相关转换
        transitions = [t for t in self.zero_count_transitions 
                      if t['from_count'] == from_count and t['to_count'] == to_count]
        
        # 找出所有起始状态
        all_from_states = [t for t in self.zero_count_transitions if t['from_count'] == from_count]
        
        if not all_from_states:
            print(f"  没有{from_count}个0分区的案例")
            return
        
        transition_prob = len(transitions) / len(all_from_states)
        print(f"  转换概率: {transition_prob:.1%} ({len(transitions)}/{len(all_from_states)})")
        
        # 分析转换条件
        if transitions:
            print(f"  转换案例示例:")
            for i, trans in enumerate(transitions[:5]):
                print(f"    {trans['from_period']} → {trans['to_period']}: "
                      f"{trans['from_pattern']} → {trans['to_pattern']}")
    
    def analyze_consecutive_stability(self, count: int, title: str):
        """分析连续性稳定性"""
        print(f"\n{title}连续性分析:")
        
        # 找出所有连续序列
        consecutive_sequences = []
        current_sequence = []
        
        for transition in self.zero_count_transitions:
            if transition['from_count'] == count:
                if not current_sequence:
                    current_sequence = [transition['from_period']]
                current_sequence.append(transition['to_period'])
                
                if transition['to_count'] != count:
                    # 序列结束
                    if len(current_sequence) > 1:
                        consecutive_sequences.append(len(current_sequence) - 1)
                    current_sequence = []
            else:
                if current_sequence and len(current_sequence) > 1:
                    consecutive_sequences.append(len(current_sequence) - 1)
                current_sequence = []
        
        if consecutive_sequences:
            max_consecutive = max(consecutive_sequences)
            avg_consecutive = np.mean(consecutive_sequences)
            
            print(f"  最长连续: {max_consecutive}期")
            print(f"  平均连续: {avg_consecutive:.1f}期")
            
            # 连续性分布
            consecutive_counter = Counter(consecutive_sequences)
            print(f"  连续性分布:")
            for length in sorted(consecutive_counter.keys()):
                freq = consecutive_counter[length]
                print(f"    连续{length}期: {freq}次")
        else:
            print(f"  没有发现连续序列")
    
    def analyze_alternating_patterns(self):
        """分析交替模式"""
        print(f"\n交替模式分析:")
        
        # 提取3和4的序列
        count_sequence = [t['from_count'] for t in self.zero_count_transitions 
                         if t['from_count'] in [3, 4]]
        
        if len(count_sequence) < 4:
            print("  数据不足，无法分析交替模式")
            return
        
        # 寻找3-4-3-4模式
        alternating_34_count = 0
        alternating_43_count = 0
        
        for i in range(len(count_sequence) - 3):
            if (count_sequence[i] == 3 and count_sequence[i+1] == 4 and 
                count_sequence[i+2] == 3 and count_sequence[i+3] == 4):
                alternating_34_count += 1
            elif (count_sequence[i] == 4 and count_sequence[i+1] == 3 and 
                  count_sequence[i+2] == 4 and count_sequence[i+3] == 3):
                alternating_43_count += 1
        
        total_possible = len(count_sequence) - 3
        alternating_prob = (alternating_34_count + alternating_43_count) / total_possible
        
        print(f"  3-4-3-4模式: {alternating_34_count}次")
        print(f"  4-3-4-3模式: {alternating_43_count}次")
        print(f"  交替模式概率: {alternating_prob:.1%}")
    
    def generate_prediction_rules(self):
        """生成预测规则"""
        print(f"\n" + "=" * 80)
        print("基于高级模式分析的预测规则")
        print("=" * 80)
        
        # 连续x规律总结
        x3_cases = [case for case in self.consecutive_x_cases if case['length'] == 3]
        x4_cases = [case for case in self.consecutive_x_cases if case['length'] == 4]
        
        if x3_cases:
            x3_next_counts = [case['next_period_info']['zero_count'] 
                             for case in x3_cases if case['next_period_info']]
            if x3_next_counts:
                x3_avg = np.mean(x3_next_counts)
                print(f"连续3个x规律:")
                print(f"  下一期平均0分区数: {x3_avg:.2f}")
        
        if x4_cases:
            x4_next_counts = [case['next_period_info']['zero_count'] 
                             for case in x4_cases if case['next_period_info']]
            if x4_next_counts:
                x4_avg = np.mean(x4_next_counts)
                print(f"连续4个x规律:")
                print(f"  下一期平均0分区数: {x4_avg:.2f}")
        
        # 转换规律总结
        transitions_3_to_4 = [t for t in self.zero_count_transitions 
                             if t['from_count'] == 3 and t['to_count'] == 4]
        all_from_3 = [t for t in self.zero_count_transitions if t['from_count'] == 3]
        
        if all_from_3:
            prob_3_to_4 = len(transitions_3_to_4) / len(all_from_3)
            print(f"\n0分区数量转换规律:")
            print(f"  3个0 → 4个0概率: {prob_3_to_4:.1%}")
        
        transitions_4_to_3 = [t for t in self.zero_count_transitions 
                             if t['from_count'] == 4 and t['to_count'] == 3]
        all_from_4 = [t for t in self.zero_count_transitions if t['from_count'] == 4]
        
        if all_from_4:
            prob_4_to_3 = len(transitions_4_to_3) / len(all_from_4)
            print(f"  4个0 → 3个0概率: {prob_4_to_3:.1%}")
        
        print(f"\n预测应用建议:")
        print(f"  1. 检测当前期是否存在连续3个或4个x")
        print(f"  2. 根据连续x长度调整下一期0分区数量预测")
        print(f"  3. 基于当前0分区数量应用转换概率")
        print(f"  4. 考虑连续性稳定性进行预测校正")


def main():
    """主函数"""
    try:
        analyzer = AdvancedPatternAnalyzer()
        analyzer.load_and_prepare_data()
        
        # 分析连续x模式
        analyzer.analyze_consecutive_x_patterns()
        
        # 分析0分区数量转换规律
        analyzer.analyze_zero_count_transitions()
        
        # 生成预测规则
        analyzer.generate_prediction_rules()
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
