#!/usr/bin/env python3
"""
连续0分区规律分析
基于o3-mini模型指导，深度分析连续两个0和连续三个0之后下一期的规律
"""
import sys
import os
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict
import scipy.stats as stats

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager


class ConsecutiveZeroAnalyzer:
    """连续0分区规律分析器"""
    
    def __init__(self):
        self.data = None
        self.zero_patterns = []
        self.consecutive_2_cases = []  # 连续2个0的案例
        self.consecutive_3_cases = []  # 连续3个0的案例
        
    def load_and_prepare_data(self):
        """加载并预处理数据"""
        print("=" * 80)
        print("连续0分区规律分析")
        print("=" * 80)
        
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        self.data = df.to_dict('records')
        
        # 预处理数据
        for record in self.data:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            zero_pattern = self.get_zero_pattern(zone_dist)
            self.zero_patterns.append({
                'period': record['期号'],
                'pattern': zero_pattern,
                'zero_count': zero_pattern.count('0'),
                'date': record.get('日期', '')
            })
        
        print(f"数据预处理完成: {len(self.data)} 条记录")
    
    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except:
            return [0] * 7
    
    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])
    
    def find_consecutive_zeros(self, pattern: str) -> List[Tuple[int, int]]:
        """找出模式中连续0的位置和长度"""
        consecutive_zeros = []
        i = 0
        while i < len(pattern):
            if pattern[i] == '0':
                start = i
                while i < len(pattern) and pattern[i] == '0':
                    i += 1
                length = i - start
                if length >= 2:  # 只关注连续2个或以上的0
                    consecutive_zeros.append((start, length))
            else:
                i += 1
        return consecutive_zeros
    
    def analyze_consecutive_patterns(self):
        """分析连续0模式"""
        print(f"\n" + "=" * 60)
        print("连续0模式识别")
        print("=" * 60)
        
        # 识别连续0案例
        for i, pattern_info in enumerate(self.zero_patterns):
            pattern = pattern_info['pattern']
            consecutive_zeros = self.find_consecutive_zeros(pattern)
            
            for start_pos, length in consecutive_zeros:
                case_info = {
                    'index': i,
                    'period': pattern_info['period'],
                    'pattern': pattern,
                    'start_pos': start_pos,
                    'length': length,
                    'positions': list(range(start_pos, start_pos + length)),
                    'next_period_info': None
                }
                
                # 获取下一期信息
                if i + 1 < len(self.zero_patterns):
                    next_info = self.zero_patterns[i + 1]
                    case_info['next_period_info'] = {
                        'period': next_info['period'],
                        'pattern': next_info['pattern'],
                        'zero_count': next_info['zero_count']
                    }
                
                # 分类存储
                if length == 2:
                    self.consecutive_2_cases.append(case_info)
                elif length == 3:
                    self.consecutive_3_cases.append(case_info)
        
        print(f"连续2个0的案例: {len(self.consecutive_2_cases)}个")
        print(f"连续3个0的案例: {len(self.consecutive_3_cases)}个")
        
        # 显示一些案例
        print(f"\n连续2个0案例示例:")
        for i, case in enumerate(self.consecutive_2_cases[:5]):
            next_info = case['next_period_info']
            if next_info:
                print(f"  {case['period']}: {case['pattern']} (位置{case['start_pos']}-{case['start_pos']+1}) "
                      f"→ {next_info['period']}: {next_info['pattern']}")
        
        print(f"\n连续3个0案例示例:")
        for i, case in enumerate(self.consecutive_3_cases[:5]):
            next_info = case['next_period_info']
            if next_info:
                print(f"  {case['period']}: {case['pattern']} (位置{case['start_pos']}-{case['start_pos']+2}) "
                      f"→ {next_info['period']}: {next_info['pattern']}")
    
    def analyze_position_dependency(self, cases: List[Dict], title: str):
        """分析位置依赖性"""
        print(f"\n" + "=" * 60)
        print(f"{title} - 位置依赖性分析")
        print("=" * 60)
        
        # 按起始位置分组
        position_groups = defaultdict(list)
        for case in cases:
            if case['next_period_info']:
                position_groups[case['start_pos']].append(case)
        
        print(f"按起始位置分组:")
        for start_pos in sorted(position_groups.keys()):
            group_cases = position_groups[start_pos]
            print(f"\n位置{start_pos}开始的连续0 ({len(group_cases)}个案例):")
            
            # 分析下一期在相同位置出现0的概率
            same_position_zeros = 0
            adjacent_position_zeros = 0
            total_next_zeros = []
            
            for case in group_cases:
                next_pattern = case['next_period_info']['pattern']
                current_positions = set(case['positions'])
                
                # 检查相同位置
                same_count = sum(1 for pos in current_positions if next_pattern[pos] == '0')
                if same_count > 0:
                    same_position_zeros += 1
                
                # 检查相邻位置
                adjacent_positions = set()
                for pos in current_positions:
                    if pos > 0:
                        adjacent_positions.add(pos - 1)
                    if pos < 6:
                        adjacent_positions.add(pos + 1)
                adjacent_positions -= current_positions
                
                adjacent_count = sum(1 for pos in adjacent_positions if next_pattern[pos] == '0')
                if adjacent_count > 0:
                    adjacent_position_zeros += 1
                
                total_next_zeros.append(case['next_period_info']['zero_count'])
            
            # 统计结果
            same_prob = same_position_zeros / len(group_cases)
            adjacent_prob = adjacent_position_zeros / len(group_cases)
            avg_next_zeros = np.mean(total_next_zeros)
            
            print(f"  相同位置延续概率: {same_prob:.1%}")
            print(f"  相邻位置出现概率: {adjacent_prob:.1%}")
            print(f"  下一期平均0分区数: {avg_next_zeros:.2f}")
    
    def analyze_quantity_changes(self, cases: List[Dict], title: str):
        """分析数量变化规律"""
        print(f"\n" + "=" * 60)
        print(f"{title} - 数量变化规律")
        print("=" * 60)
        
        # 收集下一期的0分区数量
        next_zero_counts = []
        for case in cases:
            if case['next_period_info']:
                next_zero_counts.append(case['next_period_info']['zero_count'])
        
        if not next_zero_counts:
            print("没有有效的下一期数据")
            return
        
        # 统计分析
        count_distribution = Counter(next_zero_counts)
        mean_count = np.mean(next_zero_counts)
        std_count = np.std(next_zero_counts)
        
        print(f"下一期0分区数量分布:")
        for count in sorted(count_distribution.keys()):
            freq = count_distribution[count]
            pct = freq / len(next_zero_counts) * 100
            print(f"  {count}个0分区: {freq}次 ({pct:.1f}%)")
        
        print(f"\n统计指标:")
        print(f"  平均数量: {mean_count:.2f}")
        print(f"  标准差: {std_count:.2f}")
        print(f"  样本数量: {len(next_zero_counts)}")
        
        # 与整体分布比较
        all_zero_counts = [info['zero_count'] for info in self.zero_patterns]
        overall_mean = np.mean(all_zero_counts)
        
        print(f"  整体平均: {overall_mean:.2f}")
        print(f"  差异: {mean_count - overall_mean:+.2f}")
        
        # 统计显著性检验
        if len(next_zero_counts) >= 10:
            t_stat, p_value = stats.ttest_1samp(next_zero_counts, overall_mean)
            print(f"  t检验 p值: {p_value:.4f}")
            if p_value < 0.05:
                print(f"  ✓ 与整体均值存在显著差异")
            else:
                print(f"  - 与整体均值无显著差异")
    
    def analyze_transition_patterns(self, cases: List[Dict], title: str):
        """分析转移模式"""
        print(f"\n" + "=" * 60)
        print(f"{title} - 转移模式分析")
        print("=" * 60)
        
        # 分析模式转移
        pattern_transitions = defaultdict(Counter)
        
        for case in cases:
            if case['next_period_info']:
                current_pattern = case['pattern']
                next_pattern = case['next_period_info']['pattern']
                pattern_transitions[current_pattern][next_pattern] += 1
        
        print(f"最常见的模式转移:")
        all_transitions = []
        for current, transitions in pattern_transitions.items():
            for next_pattern, count in transitions.items():
                all_transitions.append((current, next_pattern, count))
        
        all_transitions.sort(key=lambda x: x[2], reverse=True)
        for current, next_pattern, count in all_transitions[:10]:
            print(f"  {current} → {next_pattern}: {count}次")
    
    def generate_prediction_rules(self):
        """生成预测规则"""
        print(f"\n" + "=" * 80)
        print("基于连续0规律的预测规则")
        print("=" * 80)
        
        print(f"连续2个0规律总结:")
        if self.consecutive_2_cases:
            # 位置延续概率
            same_position_count = 0
            total_cases = 0
            next_zero_counts = []
            
            for case in self.consecutive_2_cases:
                if case['next_period_info']:
                    total_cases += 1
                    next_pattern = case['next_period_info']['pattern']
                    current_positions = set(case['positions'])
                    
                    same_count = sum(1 for pos in current_positions if next_pattern[pos] == '0')
                    if same_count > 0:
                        same_position_count += 1
                    
                    next_zero_counts.append(case['next_period_info']['zero_count'])
            
            if total_cases > 0:
                continuation_prob = same_position_count / total_cases
                avg_next_zeros = np.mean(next_zero_counts)
                
                print(f"  位置延续概率: {continuation_prob:.1%}")
                print(f"  下一期平均0分区数: {avg_next_zeros:.2f}")
                
                if continuation_prob > 0.4:
                    print(f"  ✓ 规则1: 连续2个0有较高概率在相同位置延续")
                
                if avg_next_zeros > 3.2:
                    print(f"  ✓ 规则2: 连续2个0后，下一期倾向于更多0分区")
        
        print(f"\n连续3个0规律总结:")
        if self.consecutive_3_cases:
            # 类似分析连续3个0
            same_position_count = 0
            total_cases = 0
            next_zero_counts = []
            
            for case in self.consecutive_3_cases:
                if case['next_period_info']:
                    total_cases += 1
                    next_pattern = case['next_period_info']['pattern']
                    current_positions = set(case['positions'])
                    
                    same_count = sum(1 for pos in current_positions if next_pattern[pos] == '0')
                    if same_count > 0:
                        same_position_count += 1
                    
                    next_zero_counts.append(case['next_period_info']['zero_count'])
            
            if total_cases > 0:
                continuation_prob = same_position_count / total_cases
                avg_next_zeros = np.mean(next_zero_counts)
                
                print(f"  位置延续概率: {continuation_prob:.1%}")
                print(f"  下一期平均0分区数: {avg_next_zeros:.2f}")
                
                if continuation_prob < 0.3:
                    print(f"  ✓ 规则3: 连续3个0后，位置倾向于转移")
                
                if avg_next_zeros < 3.0:
                    print(f"  ✓ 规则4: 连续3个0后，下一期倾向于回归均值")
        
        print(f"\n预测应用建议:")
        print(f"  1. 检测当前期是否存在连续2个或3个0")
        print(f"  2. 根据连续0的位置和长度应用相应规则")
        print(f"  3. 调整下一期预测的位置概率和数量倾向")
        print(f"  4. 与现有马尔科夫链和自适应系统融合")


def main():
    """主函数"""
    try:
        analyzer = ConsecutiveZeroAnalyzer()
        analyzer.load_and_prepare_data()
        
        # 分析连续0模式
        analyzer.analyze_consecutive_patterns()
        
        # 分析连续2个0的规律
        if analyzer.consecutive_2_cases:
            analyzer.analyze_position_dependency(analyzer.consecutive_2_cases, "连续2个0")
            analyzer.analyze_quantity_changes(analyzer.consecutive_2_cases, "连续2个0")
            analyzer.analyze_transition_patterns(analyzer.consecutive_2_cases, "连续2个0")
        
        # 分析连续3个0的规律
        if analyzer.consecutive_3_cases:
            analyzer.analyze_position_dependency(analyzer.consecutive_3_cases, "连续3个0")
            analyzer.analyze_quantity_changes(analyzer.consecutive_3_cases, "连续3个0")
            analyzer.analyze_transition_patterns(analyzer.consecutive_3_cases, "连续3个0")
        
        # 生成预测规则
        analyzer.generate_prediction_rules()
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
