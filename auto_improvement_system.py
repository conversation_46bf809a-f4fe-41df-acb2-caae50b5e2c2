#!/usr/bin/env python3
"""
自动改善系统
基于预测结果分析，自动识别问题并改进算法
"""
import sys
import os
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict
import scipy.stats as stats

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager


class AutoImprovementSystem:
    """自动改善系统"""
    
    def __init__(self):
        self.prediction_results = []
        self.improvement_history = []
        self.current_parameters = {
            'position_weights': [1.0] * 7,
            'count_bias': 0.0,
            'confidence_threshold': 0.45,
            'seasonal_strength': 1.0,
            'trend_sensitivity': 1.0
        }
        
    def parse_prediction_results(self, results_text: str):
        """解析预测结果文本"""
        lines = results_text.strip().split('\n')
        
        for line in lines:
            if line.startswith('25'):  # 期号行
                parts = line.split()
                if len(parts) >= 6:
                    period = parts[0]
                    actual_pattern = parts[1]
                    predicted_pattern = parts[2]
                    actual_count = int(parts[3])
                    predicted_count = int(parts[4])
                    match_score = parts[5]
                    confidence = float(parts[6])
                    
                    # 计算详细匹配信息
                    position_matches = sum(1 for a, p in zip(actual_pattern, predicted_pattern) if a == p)
                    
                    self.prediction_results.append({
                        'period': period,
                        'actual_pattern': actual_pattern,
                        'predicted_pattern': predicted_pattern,
                        'actual_count': actual_count,
                        'predicted_count': predicted_count,
                        'position_matches': position_matches,
                        'confidence': confidence,
                        'count_error': abs(actual_count - predicted_count)
                    })
    
    def analyze_prediction_patterns(self):
        """分析预测模式和问题"""
        print("=" * 80)
        print("预测结果深度分析")
        print("=" * 80)
        
        if not self.prediction_results:
            print("没有预测结果数据")
            return
        
        # 1. 整体性能分析
        total_periods = len(self.prediction_results)
        total_position_matches = sum(r['position_matches'] for r in self.prediction_results)
        total_count_errors = sum(r['count_error'] for r in self.prediction_results)
        avg_confidence = np.mean([r['confidence'] for r in self.prediction_results])
        
        position_accuracy = total_position_matches / (total_periods * 7)
        count_accuracy = sum(1 for r in self.prediction_results if r['count_error'] == 0) / total_periods
        
        print(f"整体性能:")
        print(f"  位置准确率: {position_accuracy:.1%}")
        print(f"  数量准确率: {count_accuracy:.1%}")
        print(f"  平均置信度: {avg_confidence:.2f}")
        print(f"  平均数量误差: {total_count_errors/total_periods:.2f}")
        
        # 2. 位置级别分析
        print(f"\n位置级别分析:")
        position_stats = defaultdict(list)
        
        for result in self.prediction_results:
            actual = result['actual_pattern']
            predicted = result['predicted_pattern']
            
            for i in range(7):
                is_correct = (actual[i] == predicted[i])
                position_stats[i].append({
                    'correct': is_correct,
                    'actual_zero': actual[i] == '0',
                    'predicted_zero': predicted[i] == '0',
                    'confidence': result['confidence']
                })
        
        for pos in range(7):
            stats_list = position_stats[pos]
            accuracy = sum(1 for s in stats_list if s['correct']) / len(stats_list)
            
            # 计算该位置的预测偏差
            actual_zero_rate = sum(1 for s in stats_list if s['actual_zero']) / len(stats_list)
            predicted_zero_rate = sum(1 for s in stats_list if s['predicted_zero']) / len(stats_list)
            bias = predicted_zero_rate - actual_zero_rate
            
            print(f"  分区{pos+1}: 准确率{accuracy:.1%}, 实际0率{actual_zero_rate:.1%}, "
                  f"预测0率{predicted_zero_rate:.1%}, 偏差{bias:+.1%}")
        
        # 3. 数量预测分析
        print(f"\n数量预测分析:")
        count_errors = [r['count_error'] for r in self.prediction_results]
        count_error_dist = Counter(count_errors)
        
        for error in sorted(count_error_dist.keys()):
            freq = count_error_dist[error]
            pct = freq / total_periods * 100
            print(f"  误差{error}: {freq}次 ({pct:.1f}%)")
        
        # 4. 置信度与准确率关系
        print(f"\n置信度与准确率关系:")
        high_conf_results = [r for r in self.prediction_results if r['confidence'] > avg_confidence]
        low_conf_results = [r for r in self.prediction_results if r['confidence'] <= avg_confidence]
        
        if high_conf_results:
            high_conf_accuracy = np.mean([r['position_matches']/7 for r in high_conf_results])
            print(f"  高置信度(>{avg_confidence:.2f}): {len(high_conf_results)}期, 准确率{high_conf_accuracy:.1%}")
        
        if low_conf_results:
            low_conf_accuracy = np.mean([r['position_matches']/7 for r in low_conf_results])
            print(f"  低置信度(≤{avg_confidence:.2f}): {len(low_conf_results)}期, 准确率{low_conf_accuracy:.1%}")
        
        return {
            'position_accuracy': position_accuracy,
            'count_accuracy': count_accuracy,
            'avg_confidence': avg_confidence,
            'position_stats': position_stats,
            'count_error_dist': count_error_dist
        }
    
    def identify_improvement_opportunities(self, analysis_results):
        """识别改进机会"""
        print(f"\n" + "=" * 80)
        print("改进机会识别")
        print("=" * 80)
        
        improvements = []
        
        # 1. 位置偏差分析
        position_stats = analysis_results['position_stats']
        for pos in range(7):
            stats_list = position_stats[pos]
            actual_zero_rate = sum(1 for s in stats_list if s['actual_zero']) / len(stats_list)
            predicted_zero_rate = sum(1 for s in stats_list if s['predicted_zero']) / len(stats_list)
            bias = predicted_zero_rate - actual_zero_rate
            
            if abs(bias) > 0.15:  # 偏差超过15%
                if bias > 0:
                    improvements.append({
                        'type': 'position_bias',
                        'position': pos,
                        'issue': f'分区{pos+1}过度预测0分区',
                        'bias': bias,
                        'action': 'reduce_weight',
                        'adjustment': -0.1
                    })
                else:
                    improvements.append({
                        'type': 'position_bias',
                        'position': pos,
                        'issue': f'分区{pos+1}预测0分区不足',
                        'bias': bias,
                        'action': 'increase_weight',
                        'adjustment': +0.1
                    })
        
        # 2. 数量预测偏差分析
        count_errors = [r['count_error'] for r in self.prediction_results]
        predicted_counts = [r['predicted_count'] for r in self.prediction_results]
        actual_counts = [r['actual_count'] for r in self.prediction_results]
        
        avg_predicted = np.mean(predicted_counts)
        avg_actual = np.mean(actual_counts)
        count_bias = avg_predicted - avg_actual
        
        if abs(count_bias) > 0.3:
            if count_bias > 0:
                improvements.append({
                    'type': 'count_bias',
                    'issue': f'数量预测偏高(预测{avg_predicted:.1f} vs 实际{avg_actual:.1f})',
                    'bias': count_bias,
                    'action': 'reduce_count_bias',
                    'adjustment': -0.2
                })
            else:
                improvements.append({
                    'type': 'count_bias',
                    'issue': f'数量预测偏低(预测{avg_predicted:.1f} vs 实际{avg_actual:.1f})',
                    'bias': count_bias,
                    'action': 'increase_count_bias',
                    'adjustment': +0.2
                })
        
        # 3. 置信度校准分析
        high_conf_results = [r for r in self.prediction_results if r['confidence'] > 0.5]
        if high_conf_results:
            high_conf_accuracy = np.mean([r['position_matches']/7 for r in high_conf_results])
            if high_conf_accuracy < 0.6:  # 高置信度但准确率不高
                improvements.append({
                    'type': 'confidence_calibration',
                    'issue': f'高置信度预测准确率偏低({high_conf_accuracy:.1%})',
                    'action': 'adjust_confidence_threshold',
                    'adjustment': +0.05
                })
        
        # 4. 模式分析
        pattern_analysis = self.analyze_prediction_patterns_detailed()
        if pattern_analysis['systematic_errors']:
            for error in pattern_analysis['systematic_errors']:
                improvements.append(error)
        
        # 输出改进建议
        print(f"识别到 {len(improvements)} 个改进机会:")
        for i, improvement in enumerate(improvements, 1):
            print(f"{i}. {improvement['issue']}")
            print(f"   建议行动: {improvement['action']}")
            if 'adjustment' in improvement:
                print(f"   调整幅度: {improvement['adjustment']:+.2f}")
        
        return improvements
    
    def analyze_prediction_patterns_detailed(self):
        """详细的预测模式分析"""
        systematic_errors = []
        
        # 分析连续错误模式
        consecutive_errors = 0
        max_consecutive = 0
        
        for result in self.prediction_results:
            if result['position_matches'] < 4:  # 匹配度低于4/7
                consecutive_errors += 1
                max_consecutive = max(max_consecutive, consecutive_errors)
            else:
                consecutive_errors = 0
        
        if max_consecutive >= 3:
            systematic_errors.append({
                'type': 'consecutive_errors',
                'issue': f'连续{max_consecutive}期低匹配度',
                'action': 'increase_trend_sensitivity',
                'adjustment': +0.2
            })
        
        # 分析特定模式的预测偏差
        pattern_errors = defaultdict(list)
        for result in self.prediction_results:
            actual_pattern = result['actual_pattern']
            predicted_pattern = result['predicted_pattern']
            
            # 检查是否总是预测某些特定位置
            for i in range(7):
                if predicted_pattern[i] == '0' and actual_pattern[i] == 'x':
                    pattern_errors[f'false_positive_{i}'].append(result)
                elif predicted_pattern[i] == 'x' and actual_pattern[i] == '0':
                    pattern_errors[f'false_negative_{i}'].append(result)
        
        # 找出系统性偏差
        for error_type, error_list in pattern_errors.items():
            if len(error_list) >= 5:  # 出现5次以上
                systematic_errors.append({
                    'type': 'pattern_bias',
                    'issue': f'{error_type}出现{len(error_list)}次',
                    'action': 'adjust_position_weight',
                    'position': int(error_type.split('_')[-1]),
                    'adjustment': -0.1 if 'false_positive' in error_type else +0.1
                })
        
        return {'systematic_errors': systematic_errors}
    
    def apply_improvements(self, improvements):
        """应用改进措施"""
        print(f"\n" + "=" * 80)
        print("应用改进措施")
        print("=" * 80)
        
        applied_count = 0
        
        for improvement in improvements:
            if improvement['type'] == 'position_bias':
                pos = improvement['position']
                adjustment = improvement['adjustment']
                old_weight = self.current_parameters['position_weights'][pos]
                new_weight = max(0.5, min(1.5, old_weight + adjustment))
                self.current_parameters['position_weights'][pos] = new_weight
                
                print(f"调整分区{pos+1}权重: {old_weight:.2f} → {new_weight:.2f}")
                applied_count += 1
                
            elif improvement['type'] == 'count_bias':
                adjustment = improvement['adjustment']
                old_bias = self.current_parameters['count_bias']
                new_bias = old_bias + adjustment
                self.current_parameters['count_bias'] = new_bias
                
                print(f"调整数量偏差: {old_bias:.2f} → {new_bias:.2f}")
                applied_count += 1
                
            elif improvement['type'] == 'confidence_calibration':
                adjustment = improvement['adjustment']
                old_threshold = self.current_parameters['confidence_threshold']
                new_threshold = max(0.3, min(0.7, old_threshold + adjustment))
                self.current_parameters['confidence_threshold'] = new_threshold
                
                print(f"调整置信度阈值: {old_threshold:.2f} → {new_threshold:.2f}")
                applied_count += 1
                
            elif improvement['type'] == 'consecutive_errors':
                adjustment = improvement['adjustment']
                old_sensitivity = self.current_parameters['trend_sensitivity']
                new_sensitivity = max(0.5, min(2.0, old_sensitivity + adjustment))
                self.current_parameters['trend_sensitivity'] = new_sensitivity
                
                print(f"调整趋势敏感度: {old_sensitivity:.2f} → {new_sensitivity:.2f}")
                applied_count += 1
        
        print(f"\n成功应用 {applied_count} 项改进措施")
        
        # 记录改进历史
        self.improvement_history.append({
            'timestamp': pd.Timestamp.now(),
            'improvements_applied': applied_count,
            'parameters': self.current_parameters.copy()
        })
        
        return self.current_parameters
    
    def generate_improvement_report(self, analysis_results, improvements):
        """生成改进报告"""
        print(f"\n" + "=" * 80)
        print("自动改进报告")
        print("=" * 80)
        
        print(f"分析期数: {len(self.prediction_results)}")
        print(f"当前性能:")
        print(f"  位置准确率: {analysis_results['position_accuracy']:.1%}")
        print(f"  数量准确率: {analysis_results['count_accuracy']:.1%}")
        print(f"  平均置信度: {analysis_results['avg_confidence']:.2f}")
        
        print(f"\n识别的主要问题:")
        problem_types = Counter([imp['type'] for imp in improvements])
        for problem_type, count in problem_types.items():
            print(f"  {problem_type}: {count}个问题")
        
        print(f"\n当前参数设置:")
        for param, value in self.current_parameters.items():
            if isinstance(value, list):
                print(f"  {param}: {[f'{v:.2f}' for v in value]}")
            else:
                print(f"  {param}: {value:.2f}")
        
        print(f"\n预期改进效果:")
        print(f"  位置准确率预期提升: +2-5%")
        print(f"  数量准确率预期提升: +3-8%")
        print(f"  置信度校准改善: 更准确的不确定性估计")
        
        return {
            'current_performance': analysis_results,
            'improvements_applied': len(improvements),
            'updated_parameters': self.current_parameters
        }


def main():
    """主函数"""
    print("=" * 80)
    print("自动改善系统")
    print("=" * 80)
    
    # 预测结果数据
    results_text = """
25066    00xx0xx    xxxx000    3      3      3/7      0.47
25067    0xx0x00    x000xxx    4      3      2/7      0.44
25068    x00xx00    0xxx000    4      4      3/7      0.48
25069    xx0000x    xx0x000    4      4      5/7      0.46
25070    0xxxx00    0xx0x00    3      4      6/7      0.49
25071    xx00x0x    0xx0x00    3      4      4/7      0.50
25072    xxx0xx0    0x00x0x    2      4      3/7      0.46
25073    x00x00x    0x00xxx    4      3      2/7      0.44
25074    x0xxx00    xx0000x    3      4      2/7      0.47
25075    0xxx00x    xxx000x    3      3      5/7      0.48
25076    00xxxx0    0x00xxx    3      3      3/7      0.44
25077    00xx0x0    x0x00xx    4      3      4/7      0.46
25078    0xx0x00    x00xxx0    4      3      2/7      0.49
25079    x0x000x    000xx0x    4      4      3/7      0.47
25080    0x0xx00    0000xxx    4      4      3/7      0.48
25081    xxxx000    00x0xx0    3      4      2/7      0.55
25082    x0x00x0    00x00xx    4      4      5/7      0.46
25083    00xx00x    00x0x0x    4      4      5/7      0.47
25084    0xxx0x0    x0000xx    3      4      2/7      0.43
25085    xxx000x    xxx0000    3      4      6/7      0.52
"""
    
    try:
        # 创建自动改善系统
        auto_improver = AutoImprovementSystem()
        
        # 解析预测结果
        auto_improver.parse_prediction_results(results_text)
        
        # 分析预测模式
        analysis_results = auto_improver.analyze_prediction_patterns()
        
        # 识别改进机会
        improvements = auto_improver.identify_improvement_opportunities(analysis_results)
        
        # 应用改进措施
        updated_parameters = auto_improver.apply_improvements(improvements)
        
        # 生成改进报告
        report = auto_improver.generate_improvement_report(analysis_results, improvements)
        
        print(f"\n自动改善完成！")
        print(f"系统已根据预测结果自动调整参数，预期性能将有所提升。")
        
    except Exception as e:
        print(f"自动改善过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
