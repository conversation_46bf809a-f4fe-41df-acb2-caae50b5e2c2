# 大乐透数据分析系统

一个基于Python和tkinter的大乐透历史数据分析应用程序。专为Python 3.13和Miniconda环境优化。

## 快速启动

```bash
# 推荐方式 - 使用tkinter启动脚本
python scripts/run_tkinter.py

# 直接启动主程序
python main.py

# 核心功能测试
python scripts/test_core.py
```

## 项目结构

```
大乐透预测0731/
├── main.py                    # 主程序入口
├── README.md                  # 项目说明
├── analysis/                  # 数据分析模块
├── controller/                # 控制器层
├── model/                     # 数据模型层
├── view/                      # 视图层(tkinter GUI)
├── tests/                     # 单元测试
├── scripts/                   # 启动脚本和工具
│   ├── run_tkinter.py        # tkinter版本启动脚本
│   ├── test_core.py          # 核心功能测试
│   └── ...                   # 其他工具脚本
├── config/                    # 配置文件
│   ├── requirements.txt      # Python依赖包
│   └── environment.yml       # Conda环境配置
├── data/                      # 数据文件
│   └── dlt_data.csv          # 大乐透历史数据(2753条)
└── docs/                      # 文档
    └── README.md             # 详细文档
```

## 环境要求

- Python 3.13+ (推荐)
- Miniconda/Anaconda
- tkinter (Python内置)
- pandas, numpy

## 功能特性

- **数据导入**: 支持CSV格式的大乐透历史数据导入
- **奇偶分析**: 计算红球奇偶比例、排布模式，并查找历史相同模式
- **分区分析**: 将1-35红球分为7个区间进行分布分析
- **零分区分析**: 专门分析分区比中为0的区间模式
- **图形界面**: 基于tkinter的直观表格展示
- **多线程处理**: 后台分析数据，保持界面响应

## 详细文档

查看 `docs/README.md` 获取完整的使用说明和技术文档。
