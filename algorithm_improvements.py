#!/usr/bin/env python3
"""
基于o3-mini算法审查的关键改进实现
直接应用到现有的adaptive_prediction_system.py中
"""
import sys
import os
import numpy as np
import logging
from typing import List, Dict, Tuple

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AlgorithmImprovements:
    """算法改进实现类"""
    
    @staticmethod
    def improved_probability_normalization(probabilities: np.ndarray) -> np.ndarray:
        """改进的概率归一化 (o3-mini建议1)"""
        try:
            # 确保所有概率为正数
            probabilities = np.maximum(probabilities, 0.01)
            
            # 重新归一化
            prob_sum = np.sum(probabilities)
            if prob_sum > 0:
                normalized = probabilities / prob_sum
            else:
                normalized = np.ones(7) / 7  # 均匀分布作为后备
            
            # 应用合理的范围限制
            normalized = np.clip(normalized, 0.05, 0.8)
            
            # 再次归一化以确保和为1
            final_sum = np.sum(normalized)
            if final_sum > 0:
                normalized = normalized / final_sum
            
            return normalized
            
        except Exception as e:
            logger.error(f"概率归一化失败: {e}")
            return np.ones(7) / 7
    
    @staticmethod
    def calculate_statistical_confidence(probabilities: np.ndarray, 
                                       selected_positions: List[int],
                                       recent_confidence: List[float]) -> float:
        """基于统计理论的置信度计算 (o3-mini建议2)"""
        try:
            # 基础置信度：选中位置的概率平均值
            base_confidence = np.mean([probabilities[pos] for pos in selected_positions])
            
            # 概率分布的熵 (越集中置信度越高)
            entropy = -np.sum(probabilities * np.log(probabilities + 1e-10))
            max_entropy = np.log(7)  # 7个位置的最大熵
            entropy_factor = 1.0 - (entropy / max_entropy)
            
            # 历史性能因子
            if len(recent_confidence) > 0:
                historical_factor = np.mean(recent_confidence[-10:])
            else:
                historical_factor = 0.5
            
            # 综合置信度
            confidence = (base_confidence * 0.5 + 
                         entropy_factor * 0.3 + 
                         historical_factor * 0.2)
            
            return np.clip(confidence, 0.1, 0.9)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.5
    
    @staticmethod
    def dynamic_weight_adjustment(markov_weight: float, adaptive_weight: float,
                                recent_performance: List[Tuple[float, float]],
                                adjustment_factor: float = 0.1) -> Tuple[float, float]:
        """动态权重调整 (o3-mini建议3)"""
        try:
            if len(recent_performance) >= 10:
                # 计算最近性能
                recent_markov_acc = np.mean([perf[0] for perf in recent_performance[-10:]])
                recent_adaptive_acc = np.mean([perf[1] for perf in recent_performance[-10:]])
                
                # 动态调整权重
                if recent_markov_acc > recent_adaptive_acc + 0.05:  # 马尔科夫明显更好
                    new_markov_weight = min(0.8, markov_weight + adjustment_factor)
                    new_adaptive_weight = 1.0 - new_markov_weight
                    logger.info(f"调整权重: 马尔科夫 {new_markov_weight:.2f}, 自适应 {new_adaptive_weight:.2f}")
                    return new_markov_weight, new_adaptive_weight
                elif recent_adaptive_acc > recent_markov_acc + 0.05:  # 自适应明显更好
                    new_adaptive_weight = min(0.8, adaptive_weight + adjustment_factor)
                    new_markov_weight = 1.0 - new_adaptive_weight
                    logger.info(f"调整权重: 马尔科夫 {new_markov_weight:.2f}, 自适应 {new_adaptive_weight:.2f}")
                    return new_markov_weight, new_adaptive_weight
            
            return markov_weight, adaptive_weight
            
        except Exception as e:
            logger.error(f"动态权重调整失败: {e}")
            return markov_weight, adaptive_weight
    
    @staticmethod
    def efficient_pattern_generation(probabilities: np.ndarray, 
                                   target_count: int,
                                   exclude_patterns: set,
                                   feasible_patterns: set,
                                   max_attempts: int = 30) -> Tuple[List[int], str]:
        """高效的模式生成 (o3-mini建议4)"""
        try:
            # 使用numpy的高效采样
            for attempt in range(max_attempts):
                # 基于概率的加权采样
                selected_positions = np.random.choice(
                    7, size=target_count, replace=False, p=probabilities
                )
                selected_positions = sorted(selected_positions.tolist())
                
                # 生成模式
                pattern = ['x'] * 7
                for pos in selected_positions:
                    pattern[pos] = '0'
                pattern_str = ''.join(pattern)
                
                # 检查约束
                if (pattern_str not in exclude_patterns and 
                    pattern_str in feasible_patterns):
                    return selected_positions, pattern_str
            
            # 如果失败，使用回退策略
            logger.warning(f"模式生成失败，使用回退策略")
            return AlgorithmImprovements.fallback_pattern_generation(target_count, feasible_patterns)
            
        except Exception as e:
            logger.error(f"模式生成失败: {e}")
            return AlgorithmImprovements.fallback_pattern_generation(target_count, feasible_patterns)
    
    @staticmethod
    def fallback_pattern_generation(target_count: int, 
                                  feasible_patterns: set) -> Tuple[List[int], str]:
        """回退模式生成策略 (o3-mini建议5)"""
        try:
            # 从可行模式中随机选择一个
            if feasible_patterns:
                pattern_str = np.random.choice(list(feasible_patterns))
                selected_positions = [i for i, char in enumerate(pattern_str) if char == '0']
                return selected_positions, pattern_str
            else:
                # 最后的回退：生成均匀分布的模式
                selected_positions = np.random.choice(7, size=target_count, replace=False).tolist()
                pattern = ['x'] * 7
                for pos in selected_positions:
                    pattern[pos] = '0'
                return selected_positions, ''.join(pattern)
                
        except Exception as e:
            logger.error(f"回退模式生成失败: {e}")
            # 最简单的回退
            positions = list(range(target_count))
            pattern = ['0'] * target_count + ['x'] * (7 - target_count)
            return positions, ''.join(pattern)
    
    @staticmethod
    def improved_consecutive_boost(probabilities: np.ndarray, 
                                 consecutive_zeros: List[Tuple[int, int]],
                                 consecutive_rules: Dict) -> np.ndarray:
        """改进的连续0增强 (o3-mini建议6)"""
        try:
            enhanced = probabilities.copy()
            
            for start_pos, length in consecutive_zeros:
                if length == 2:
                    rule = consecutive_rules.get('consecutive_2', {})
                elif length >= 3:
                    rule = consecutive_rules.get('consecutive_3', {})
                else:
                    continue
                
                # 改进的增强计算 - 降低增强系数
                base_boost = rule.get('base_boost', 0.3)  # 从0.4/0.6降低到0.3/0.4
                adjacent_boost = rule.get('adjacent_boost', 0.15)  # 从0.2/0.3降低到0.15/0.2
                confidence_factor = rule.get('confidence_factor', 0.8)
                
                boost_factor = base_boost * confidence_factor
                adjacent_factor = adjacent_boost * confidence_factor
                
                # 相同位置增强
                for pos in range(start_pos, start_pos + length):
                    if pos < 7:
                        enhanced[pos] += boost_factor
                
                # 相邻位置增强
                if start_pos > 0:
                    enhanced[start_pos - 1] += adjacent_factor
                if start_pos + length < 7:
                    enhanced[start_pos + length] += adjacent_factor
            
            return AlgorithmImprovements.improved_probability_normalization(enhanced)
            
        except Exception as e:
            logger.error(f"连续0增强失败: {e}")
            return probabilities


def demonstrate_improvements():
    """演示算法改进效果"""
    print("=" * 80)
    print("基于o3-mini算法审查的关键改进演示")
    print("=" * 80)
    
    # 1. 概率归一化改进演示
    print("\n1. 改进的概率归一化:")
    original_probs = np.array([0.8, 1.2, 0.3, 0.9, 0.1, 0.7, 1.1])  # 不规范的概率
    print(f"  原始概率: {original_probs}")
    print(f"  原始和: {np.sum(original_probs):.3f}")
    
    improved_probs = AlgorithmImprovements.improved_probability_normalization(original_probs)
    print(f"  改进后概率: {improved_probs}")
    print(f"  改进后和: {np.sum(improved_probs):.3f}")
    print(f"  ✓ 确保概率和为1，范围在[0.05, 0.8]内")
    
    # 2. 统计置信度计算演示
    print("\n2. 基于统计理论的置信度计算:")
    selected_positions = [1, 3, 5]
    recent_confidence = [0.4, 0.5, 0.3, 0.6, 0.4]
    
    confidence = AlgorithmImprovements.calculate_statistical_confidence(
        improved_probs, selected_positions, recent_confidence
    )
    print(f"  选中位置: {selected_positions}")
    print(f"  计算置信度: {confidence:.3f}")
    print(f"  ✓ 结合概率分布熵和历史性能")
    
    # 3. 动态权重调整演示
    print("\n3. 动态权重调整:")
    markov_weight, adaptive_weight = 0.5, 0.5
    recent_performance = [(0.6, 0.4), (0.7, 0.5), (0.65, 0.45), (0.8, 0.5)]  # (马尔科夫, 自适应)
    
    new_markov, new_adaptive = AlgorithmImprovements.dynamic_weight_adjustment(
        markov_weight, adaptive_weight, recent_performance
    )
    print(f"  原权重: 马尔科夫 {markov_weight}, 自适应 {adaptive_weight}")
    print(f"  新权重: 马尔科夫 {new_markov:.2f}, 自适应 {new_adaptive:.2f}")
    print(f"  ✓ 根据性能自动调整权重")
    
    # 4. 高效模式生成演示
    print("\n4. 高效模式生成:")
    exclude_patterns = {'xxx0000', '0000xxx'}
    feasible_patterns = {'xx00x0x', 'x0x0x0x', '0x0x00x', 'x00xx0x'}
    
    positions, pattern = AlgorithmImprovements.efficient_pattern_generation(
        improved_probs, 3, exclude_patterns, feasible_patterns
    )
    print(f"  生成模式: {pattern}")
    print(f"  选中位置: {positions}")
    print(f"  ✓ 基于概率的高效采样")
    
    print(f"\n改进总结:")
    print(f"  ✅ 概率归一化: 确保数学正确性和数值稳定性")
    print(f"  ✅ 置信度计算: 基于信息论和统计学理论")
    print(f"  ✅ 动态权重: 根据实际性能自适应调整")
    print(f"  ✅ 高效生成: numpy矢量化操作提升性能")
    print(f"  ✅ 错误处理: 完善的异常处理和日志记录")
    print(f"  ✅ 回退策略: 确保系统在极端情况下的鲁棒性")


def main():
    """主函数"""
    try:
        demonstrate_improvements()
        
        print(f"\n" + "=" * 80)
        print("应用建议:")
        print("=" * 80)
        print(f"1. 将这些改进方法集成到adaptive_prediction_system.py中")
        print(f"2. 替换现有的概率归一化和置信度计算方法")
        print(f"3. 添加动态权重调整机制")
        print(f"4. 优化模式生成算法")
        print(f"5. 增强错误处理和日志记录")
        print(f"6. 进行A/B测试验证改进效果")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
