#!/usr/bin/env python3
"""
改进的自适应预测系统
基于o3-mini算法审查建议，修复不合理的设计和实现
"""
import sys
import os
import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional
from collections import Counter, defaultdict
from sklearn.metrics import accuracy_score
import scipy.stats as stats

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager
from model.markov_chain import SegmentedMarkovChain

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ImprovedAdaptiveSystem:
    """改进的自适应预测系统"""
    
    def __init__(self):
        # 基础参数
        self.position_weights = np.array([1.0, 1.0, 0.9, 0.9, 1.0, 1.0, 1.1])
        self.confidence_threshold = 0.50
        self.trend_sensitivity = 1.20
        
        # 性能跟踪
        self.recent_accuracy = []
        self.recent_confidence = []
        self.adaptation_count = 0
        
        # 动态权重系统 (改进点1: 动态权重调整)
        self.markov_weight = 0.5
        self.adaptive_weight = 0.5
        self.weight_adjustment_factor = 0.1
        self.performance_window = 10  # 用于评估性能的窗口大小
        
        # 马尔科夫链
        self.markov_chain = SegmentedMarkovChain(order=1)
        self.markov_trained = False
        
        # 改进的连续0规律参数 (改进点2: 基于统计的增强系数)
        self.consecutive_rules = {
            'consecutive_2': {
                'position_continuation_prob': 0.721,
                'base_boost': 0.3,  # 降低基础增强
                'adjacent_boost': 0.15,  # 降低相邻增强
                'confidence_factor': 0.8  # 置信度因子
            },
            'consecutive_3': {
                'position_continuation_prob': 0.902,
                'base_boost': 0.4,  # 降低基础增强
                'adjacent_boost': 0.2,  # 降低相邻增强
                'confidence_factor': 0.9  # 置信度因子
            }
        }
        
        # 高级模式规律参数
        self.advanced_rules = {
            'consecutive_x': {
                'x3_avg_next_zeros': 3.13,
                'x4_avg_next_zeros': 3.13,
                'x5_avg_next_zeros': 2.97
            },
            'zero_transitions': {
                'prob_3_to_4': 0.280,
                'prob_4_to_3': 0.524,
                'stability_3': 2.1,
                'stability_4': 1.4
            }
        }
        
        # 位置相关性矩阵 (简化版)
        self.position_correlations = np.array([
            [ 1.00, -0.15, -0.10, -0.05,  0.00,  0.05,  0.10],
            [-0.15,  1.00, -0.15, -0.10, -0.05,  0.00,  0.05],
            [-0.10, -0.15,  1.00, -0.15, -0.10, -0.05,  0.00],
            [-0.05, -0.10, -0.15,  1.00, -0.15, -0.10, -0.05],
            [ 0.00, -0.05, -0.10, -0.15,  1.00, -0.15, -0.10],
            [ 0.05,  0.00, -0.05, -0.10, -0.15,  1.00, -0.15],
            [ 0.10,  0.05,  0.00, -0.05, -0.10, -0.15,  1.00]
        ])
    
    def parse_zone_ratio(self, zone_ratio_str: str) -> List[int]:
        """解析分区比字符串"""
        try:
            return [int(part) for part in zone_ratio_str.split(':')]
        except Exception as e:
            logger.warning(f"解析分区比失败: {zone_ratio_str}, 错误: {e}")
            return [0] * 7
    
    def get_zero_pattern(self, zone_distribution: List[int]) -> str:
        """获取0分区模式"""
        return ''.join(['0' if count == 0 else 'x' for count in zone_distribution])
    
    def update_dynamic_weights(self, markov_accuracy: float, adaptive_accuracy: float):
        """动态调整融合权重 (改进点1)"""
        try:
            if len(self.recent_accuracy) >= self.performance_window:
                # 计算最近性能
                recent_markov_acc = np.mean([acc[0] for acc in self.recent_accuracy[-self.performance_window:]])
                recent_adaptive_acc = np.mean([acc[1] for acc in self.recent_accuracy[-self.performance_window:]])
                
                # 动态调整权重
                if recent_markov_acc > recent_adaptive_acc + 0.05:  # 马尔科夫明显更好
                    self.markov_weight = min(0.8, self.markov_weight + self.weight_adjustment_factor)
                    self.adaptive_weight = 1.0 - self.markov_weight
                    logger.info(f"调整权重: 马尔科夫 {self.markov_weight:.2f}, 自适应 {self.adaptive_weight:.2f}")
                elif recent_adaptive_acc > recent_markov_acc + 0.05:  # 自适应明显更好
                    self.adaptive_weight = min(0.8, self.adaptive_weight + self.weight_adjustment_factor)
                    self.markov_weight = 1.0 - self.adaptive_weight
                    logger.info(f"调整权重: 马尔科夫 {self.markov_weight:.2f}, 自适应 {self.adaptive_weight:.2f}")
                
        except Exception as e:
            logger.error(f"动态权重调整失败: {e}")
    
    def improved_probability_normalization(self, probabilities: np.ndarray) -> np.ndarray:
        """改进的概率归一化 (改进点2)"""
        try:
            # 确保所有概率为正数
            probabilities = np.maximum(probabilities, 0.01)
            
            # 重新归一化
            prob_sum = np.sum(probabilities)
            if prob_sum > 0:
                normalized = probabilities / prob_sum
            else:
                normalized = np.ones(7) / 7  # 均匀分布作为后备
            
            # 应用合理的范围限制
            normalized = np.clip(normalized, 0.05, 0.8)
            
            # 再次归一化以确保和为1
            final_sum = np.sum(normalized)
            if final_sum > 0:
                normalized = normalized / final_sum
            
            return normalized
            
        except Exception as e:
            logger.error(f"概率归一化失败: {e}")
            return np.ones(7) / 7
    
    def calculate_statistical_confidence(self, probabilities: np.ndarray, 
                                       selected_positions: List[int]) -> float:
        """基于统计理论的置信度计算 (改进点3)"""
        try:
            # 基础置信度：选中位置的概率平均值
            base_confidence = np.mean([probabilities[pos] for pos in selected_positions])
            
            # 概率分布的熵 (越集中置信度越高)
            entropy = -np.sum(probabilities * np.log(probabilities + 1e-10))
            max_entropy = np.log(7)  # 7个位置的最大熵
            entropy_factor = 1.0 - (entropy / max_entropy)
            
            # 历史性能因子
            if len(self.recent_confidence) > 0:
                historical_factor = np.mean(self.recent_confidence[-10:])
            else:
                historical_factor = 0.5
            
            # 综合置信度
            confidence = (base_confidence * 0.5 + 
                         entropy_factor * 0.3 + 
                         historical_factor * 0.2)
            
            return np.clip(confidence, 0.1, 0.9)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.5
    
    def efficient_pattern_generation(self, probabilities: np.ndarray, 
                                   target_count: int,
                                   exclude_patterns: set,
                                   feasible_patterns: set,
                                   max_attempts: int = 50) -> Tuple[Optional[List[int]], Optional[str]]:
        """高效的模式生成 (改进点4)"""
        try:
            # 使用numpy的高效采样
            for attempt in range(max_attempts):
                # 基于概率的加权采样
                selected_positions = np.random.choice(
                    7, size=target_count, replace=False, p=probabilities
                )
                selected_positions = sorted(selected_positions.tolist())
                
                # 生成模式
                pattern = ['x'] * 7
                for pos in selected_positions:
                    pattern[pos] = '0'
                pattern_str = ''.join(pattern)
                
                # 检查约束
                if (pattern_str not in exclude_patterns and 
                    pattern_str in feasible_patterns):
                    return selected_positions, pattern_str
            
            # 如果失败，使用回退策略
            logger.warning(f"模式生成失败，使用回退策略")
            return self.fallback_pattern_generation(target_count, feasible_patterns)
            
        except Exception as e:
            logger.error(f"模式生成失败: {e}")
            return None, None
    
    def fallback_pattern_generation(self, target_count: int, 
                                  feasible_patterns: set) -> Tuple[List[int], str]:
        """回退模式生成策略 (改进点5)"""
        try:
            # 从可行模式中随机选择一个
            if feasible_patterns:
                pattern_str = np.random.choice(list(feasible_patterns))
                selected_positions = [i for i, char in enumerate(pattern_str) if char == '0']
                return selected_positions, pattern_str
            else:
                # 最后的回退：生成均匀分布的模式
                selected_positions = np.random.choice(7, size=target_count, replace=False).tolist()
                pattern = ['x'] * 7
                for pos in selected_positions:
                    pattern[pos] = '0'
                return selected_positions, ''.join(pattern)
                
        except Exception as e:
            logger.error(f"回退模式生成失败: {e}")
            # 最简单的回退
            positions = list(range(target_count))
            pattern = ['0'] * target_count + ['x'] * (7 - target_count)
            return positions, ''.join(pattern)
    
    def validate_statistical_assumptions(self, recent_data: List[Dict]) -> Dict[str, bool]:
        """验证统计假设 (改进点6)"""
        try:
            validation_results = {}
            
            if len(recent_data) < 30:
                validation_results['sufficient_sample'] = False
                logger.warning("样本数量不足，统计推断可能不可靠")
            else:
                validation_results['sufficient_sample'] = True
            
            # 检查数据的正态性
            zero_counts = []
            for record in recent_data[-50:]:  # 使用最近50期
                zone_dist = self.parse_zone_ratio(record['分区比'])
                pattern = self.get_zero_pattern(zone_dist)
                zero_counts.append(pattern.count('0'))
            
            if len(zero_counts) >= 8:  # 最小样本要求
                _, p_value = stats.shapiro(zero_counts)
                validation_results['normality'] = p_value > 0.05
                if not validation_results['normality']:
                    logger.info("数据不符合正态分布，建议使用非参数方法")
            else:
                validation_results['normality'] = False
            
            return validation_results
            
        except Exception as e:
            logger.error(f"统计假设验证失败: {e}")
            return {'sufficient_sample': False, 'normality': False}

    def improved_adaptive_probabilities(self, recent_data: List[Dict]) -> np.ndarray:
        """改进的自适应概率计算"""
        try:
            # 验证统计假设
            validation = self.validate_statistical_assumptions(recent_data)

            # 基础概率计算
            base_probabilities = np.copy(self.position_weights)

            # 历史频率分析 (使用更大的窗口)
            window_size = min(100, len(recent_data))
            position_counts = np.zeros(7)

            for record in recent_data[-window_size:]:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                pattern = self.get_zero_pattern(zone_dist)
                for i, char in enumerate(pattern):
                    if char == '0':
                        position_counts[i] += 1

            # 计算频率并平滑
            if window_size > 0:
                frequencies = position_counts / window_size
                # 使用贝叶斯平滑
                alpha = 1.0  # 平滑参数
                smoothed_freq = (position_counts + alpha) / (window_size + 7 * alpha)
                base_probabilities = base_probabilities * 0.7 + smoothed_freq * 0.3

            # 趋势分析 (改进的统计方法)
            if len(recent_data) >= 20:
                recent_trends = self.calculate_robust_trends(recent_data[-20:])
                base_probabilities += recent_trends * self.trend_sensitivity * 0.1

            # 相关性调整 (矢量化操作)
            correlation_adjustment = np.dot(self.position_correlations, base_probabilities) * 0.1
            base_probabilities += correlation_adjustment

            # 改进的归一化
            return self.improved_probability_normalization(base_probabilities)

        except Exception as e:
            logger.error(f"自适应概率计算失败: {e}")
            return np.ones(7) / 7

    def calculate_robust_trends(self, recent_data: List[Dict]) -> np.ndarray:
        """计算稳健的趋势 (改进点7)"""
        try:
            trends = np.zeros(7)

            if len(recent_data) < 10:
                return trends

            # 使用滑动窗口计算趋势
            window1 = recent_data[-10:]  # 最近10期
            window2 = recent_data[-20:-10] if len(recent_data) >= 20 else recent_data[:-10]

            if len(window2) == 0:
                return trends

            # 计算两个窗口的平均频率
            freq1 = self.calculate_position_frequencies(window1)
            freq2 = self.calculate_position_frequencies(window2)

            # 使用稳健的差异计算
            trends = freq1 - freq2

            # 应用统计显著性检验
            for i in range(7):
                counts1 = sum(1 for record in window1
                             if self.get_zero_pattern(self.parse_zone_ratio(record['分区比']))[i] == '0')
                counts2 = sum(1 for record in window2
                             if self.get_zero_pattern(self.parse_zone_ratio(record['分区比']))[i] == '0')

                # 使用卡方检验或Fisher精确检验
                if len(window1) >= 5 and len(window2) >= 5:
                    contingency = [[counts1, len(window1) - counts1],
                                 [counts2, len(window2) - counts2]]
                    try:
                        _, p_value = stats.fisher_exact(contingency)
                        if p_value > 0.05:  # 不显著
                            trends[i] *= 0.5  # 降低权重
                    except:
                        pass  # 如果检验失败，保持原趋势

            return np.clip(trends, -0.2, 0.2)  # 限制趋势幅度

        except Exception as e:
            logger.error(f"趋势计算失败: {e}")
            return np.zeros(7)

    def calculate_position_frequencies(self, data: List[Dict]) -> np.ndarray:
        """计算位置频率"""
        try:
            frequencies = np.zeros(7)
            if not data:
                return frequencies

            for record in data:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                pattern = self.get_zero_pattern(zone_dist)
                for i, char in enumerate(pattern):
                    if char == '0':
                        frequencies[i] += 1

            return frequencies / len(data)

        except Exception as e:
            logger.error(f"频率计算失败: {e}")
            return np.zeros(7)

    def generate_improved_prediction(self, recent_data: List[Dict]) -> Dict:
        """生成改进的预测"""
        try:
            logger.info("开始生成改进预测")

            # 获取约束条件
            exclude_patterns, exclude_counts = self.get_recent_patterns_and_counts(recent_data, 50)
            feasible_patterns, feasible_counts = self.get_historical_feasible_patterns(recent_data, 300)

            # 训练马尔科夫链
            if not self.markov_trained and len(recent_data) >= 50:
                self.train_markov_chain(recent_data[-200:])

            # 获取马尔科夫预测
            markov_pattern, markov_confidence, markov_count = self.get_markov_prediction(recent_data)

            # 计算改进的自适应概率
            adaptive_probabilities = self.improved_adaptive_probabilities(recent_data)

            # 应用连续0规律增强
            enhanced_probabilities = self.apply_improved_consecutive_boost(
                adaptive_probabilities, recent_data
            )

            # 动态权重融合
            final_probabilities = (enhanced_probabilities * self.adaptive_weight +
                                 self.get_markov_boost(markov_pattern) * self.markov_weight)

            # 最终归一化
            final_probabilities = self.improved_probability_normalization(final_probabilities)

            # 智能数量预测
            target_count = self.improved_count_prediction(recent_data, feasible_counts)

            # 高效模式生成
            selected_positions, predicted_pattern = self.efficient_pattern_generation(
                final_probabilities, target_count, exclude_patterns, feasible_patterns
            )

            if selected_positions is None:
                logger.warning("模式生成失败，使用回退策略")
                selected_positions, predicted_pattern = self.fallback_pattern_generation(
                    target_count, feasible_patterns
                )

            # 计算改进的置信度
            confidence = self.calculate_statistical_confidence(final_probabilities, selected_positions)

            # 更新性能跟踪
            self.recent_confidence.append(confidence)
            if len(self.recent_confidence) > 20:
                self.recent_confidence.pop(0)

            return {
                'predicted_pattern': predicted_pattern,
                'predicted_count': target_count,
                'position_probabilities': final_probabilities.tolist(),
                'confidence': confidence,
                'selected_positions': selected_positions,
                'method': 'improved_adaptive_system',
                'markov_weight': self.markov_weight,
                'adaptive_weight': self.adaptive_weight,
                'validation_passed': len(exclude_patterns) > 0 and len(feasible_patterns) > 0
            }

        except Exception as e:
            logger.error(f"预测生成失败: {e}")
            return self.generate_fallback_prediction(recent_data)

    def get_recent_patterns_and_counts(self, recent_data: List[Dict], exclude_periods: int = 50) -> Tuple[set, set]:
        """获取最近N期出现过的模式和数量"""
        exclude_patterns = set()
        exclude_counts = set()

        if len(recent_data) < exclude_periods:
            exclude_periods = len(recent_data)

        for record in recent_data[-exclude_periods:]:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            pattern = self.get_zero_pattern(zone_dist)
            zero_count = pattern.count('0')

            exclude_patterns.add(pattern)
            exclude_counts.add(zero_count)

        return exclude_patterns, exclude_counts

    def get_historical_feasible_patterns(self, recent_data: List[Dict], history_periods: int = 300) -> Tuple[set, set]:
        """获取历史可行的模式和数量"""
        feasible_patterns = set()
        feasible_counts = set()

        if len(recent_data) < history_periods:
            history_periods = len(recent_data)

        for record in recent_data[-history_periods:]:
            zone_dist = self.parse_zone_ratio(record['分区比'])
            pattern = self.get_zero_pattern(zone_dist)
            zero_count = pattern.count('0')

            feasible_patterns.add(pattern)
            feasible_counts.add(zero_count)

        return feasible_patterns, feasible_counts

    def train_markov_chain(self, training_data: List[Dict]):
        """训练马尔科夫链"""
        try:
            if len(training_data) < 20:
                logger.warning("训练数据不足")
                return

            patterns = []
            for record in training_data:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                pattern = self.get_zero_pattern(zone_dist)
                patterns.append(pattern)

            self.markov_chain.train(patterns)
            self.markov_trained = True
            logger.info(f"马尔科夫链训练完成，使用{len(patterns)}个样本")

        except Exception as e:
            logger.error(f"马尔科夫链训练失败: {e}")

    def get_markov_prediction(self, recent_data: List[Dict]) -> Tuple[str, float, int]:
        """获取马尔科夫预测"""
        try:
            if not self.markov_trained or len(recent_data) == 0:
                return "xxx0000", 0.3, 4

            last_pattern = self.get_zero_pattern(
                self.parse_zone_ratio(recent_data[-1]['分区比'])
            )

            prediction = self.markov_chain.predict(last_pattern)
            if prediction:
                confidence = 0.6
                count = prediction.count('0')
                return prediction, confidence, count
            else:
                return "xxx0000", 0.3, 4

        except Exception as e:
            logger.error(f"马尔科夫预测失败: {e}")
            return "xxx0000", 0.3, 4

    def get_markov_boost(self, markov_pattern: str) -> np.ndarray:
        """获取马尔科夫增强"""
        boost = np.zeros(7)
        for i, char in enumerate(markov_pattern):
            if char == '0':
                boost[i] = 0.3
        return boost

    def apply_improved_consecutive_boost(self, probabilities: np.ndarray, recent_data: List[Dict]) -> np.ndarray:
        """应用改进的连续0增强"""
        if not recent_data:
            return probabilities

        enhanced = probabilities.copy()

        try:
            last_record = recent_data[-1]
            zone_dist = self.parse_zone_ratio(last_record['分区比'])
            last_pattern = self.get_zero_pattern(zone_dist)

            consecutive_zeros = self.find_consecutive_zeros(last_pattern)

            for start_pos, length in consecutive_zeros:
                if length == 2:
                    rule = self.consecutive_rules['consecutive_2']
                elif length >= 3:
                    rule = self.consecutive_rules['consecutive_3']
                else:
                    continue

                # 改进的增强计算
                boost_factor = rule['base_boost'] * rule['confidence_factor']
                adjacent_factor = rule['adjacent_boost'] * rule['confidence_factor']

                # 相同位置增强
                for pos in range(start_pos, start_pos + length):
                    if pos < 7:
                        enhanced[pos] += boost_factor

                # 相邻位置增强
                if start_pos > 0:
                    enhanced[start_pos - 1] += adjacent_factor
                if start_pos + length < 7:
                    enhanced[start_pos + length] += adjacent_factor

            return self.improved_probability_normalization(enhanced)

        except Exception as e:
            logger.error(f"连续0增强失败: {e}")
            return probabilities

    def find_consecutive_zeros(self, pattern: str) -> List[Tuple[int, int]]:
        """找出连续0的位置和长度"""
        consecutive_zeros = []
        i = 0
        while i < len(pattern):
            if pattern[i] == '0':
                start = i
                while i < len(pattern) and pattern[i] == '0':
                    i += 1
                length = i - start
                if length >= 2:
                    consecutive_zeros.append((start, length))
            else:
                i += 1
        return consecutive_zeros

    def improved_count_prediction(self, recent_data: List[Dict], feasible_counts: set) -> int:
        """改进的数量预测"""
        try:
            if not recent_data:
                return 3

            # 基于历史统计的预测
            recent_counts = []
            for record in recent_data[-20:]:
                zone_dist = self.parse_zone_ratio(record['分区比'])
                pattern = self.get_zero_pattern(zone_dist)
                recent_counts.append(pattern.count('0'))

            if recent_counts:
                predicted_count = int(round(np.mean(recent_counts)))
            else:
                predicted_count = 3

            # 确保在可行范围内
            if predicted_count not in feasible_counts and feasible_counts:
                feasible_list = sorted(list(feasible_counts))
                predicted_count = min(feasible_list, key=lambda x: abs(x - predicted_count))

            return max(2, min(5, predicted_count))

        except Exception as e:
            logger.error(f"数量预测失败: {e}")
            return 3

    def generate_fallback_prediction(self, recent_data: List[Dict]) -> Dict:
        """生成回退预测"""
        return {
            'predicted_pattern': 'xxx0000',
            'predicted_count': 4,
            'position_probabilities': (np.ones(7) / 7).tolist(),
            'confidence': 0.3,
            'selected_positions': [3, 4, 5, 6],
            'method': 'fallback',
            'markov_weight': 0.5,
            'adaptive_weight': 0.5,
            'validation_passed': False
        }
