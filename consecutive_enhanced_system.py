#!/usr/bin/env python3
"""
集成连续0规律的增强预测系统
基于o3-mini分析结果，将连续0规律融入自适应马尔科夫预测系统
"""
import sys
import os
import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.lottery_config import LotteryType
from model.db_manager import SQLiteManager
from adaptive_prediction_system import AdaptivePredictionSystem


class ConsecutiveEnhancedSystem(AdaptivePredictionSystem):
    """集成连续0规律的增强预测系统"""
    
    def __init__(self):
        super().__init__()
        
        # 基于分析结果的连续0规律参数
        self.consecutive_rules = {
            'consecutive_2': {
                'position_continuation_prob': 0.721,  # 72.1%位置延续概率
                'avg_next_zeros': 3.13,
                'position_boost': 0.4,  # 相同位置增强系数
                'adjacent_boost': 0.2   # 相邻位置增强系数
            },
            'consecutive_3': {
                'position_continuation_prob': 0.902,  # 90.2%位置延续概率
                'avg_next_zeros': 3.14,
                'position_boost': 0.6,  # 更强的相同位置增强
                'adjacent_boost': 0.3   # 相邻位置增强系数
            }
        }
    
    def find_consecutive_zeros(self, pattern: str) -> List[Tuple[int, int]]:
        """找出模式中连续0的位置和长度"""
        consecutive_zeros = []
        i = 0
        while i < len(pattern):
            if pattern[i] == '0':
                start = i
                while i < len(pattern) and pattern[i] == '0':
                    i += 1
                length = i - start
                if length >= 2:  # 只关注连续2个或以上的0
                    consecutive_zeros.append((start, length))
            else:
                i += 1
        return consecutive_zeros
    
    def apply_consecutive_zero_boost(self, position_probabilities: np.ndarray, 
                                   recent_data: List[Dict]) -> np.ndarray:
        """应用连续0规律增强"""
        if not recent_data:
            return position_probabilities
        
        # 获取最近一期的模式
        last_record = recent_data[-1]
        zone_dist = self.parse_zone_ratio(last_record['分区比'])
        last_pattern = self.get_zero_pattern(zone_dist)
        
        # 检测连续0
        consecutive_zeros = self.find_consecutive_zeros(last_pattern)
        
        if not consecutive_zeros:
            return position_probabilities
        
        enhanced_probabilities = position_probabilities.copy()
        
        print(f"  检测到连续0: {consecutive_zeros}")
        
        for start_pos, length in consecutive_zeros:
            if length == 2:
                rule = self.consecutive_rules['consecutive_2']
                rule_name = "连续2个0"
            elif length >= 3:
                rule = self.consecutive_rules['consecutive_3']
                rule_name = "连续3个0"
            else:
                continue
            
            print(f"  应用{rule_name}规律 (位置{start_pos}-{start_pos+length-1})")
            
            # 增强相同位置的概率
            for pos in range(start_pos, start_pos + length):
                if pos < 7:
                    boost = rule['position_boost'] * rule['position_continuation_prob']
                    enhanced_probabilities[pos] += boost
                    print(f"    位置{pos}: +{boost:.3f} (相同位置增强)")
            
            # 增强相邻位置的概率
            adjacent_positions = []
            if start_pos > 0:
                adjacent_positions.append(start_pos - 1)
            if start_pos + length < 7:
                adjacent_positions.append(start_pos + length)
            
            for pos in adjacent_positions:
                boost = rule['adjacent_boost']
                enhanced_probabilities[pos] += boost
                print(f"    位置{pos}: +{boost:.3f} (相邻位置增强)")
        
        # 确保概率在合理范围内
        enhanced_probabilities = np.clip(enhanced_probabilities, 0.1, 0.9)
        
        return enhanced_probabilities
    
    def predict_consecutive_zero_count(self, recent_data: List[Dict]) -> float:
        """基于连续0规律预测数量倾向"""
        if not recent_data:
            return 3.13  # 默认均值
        
        # 获取最近一期的模式
        last_record = recent_data[-1]
        zone_dist = self.parse_zone_ratio(last_record['分区比'])
        last_pattern = self.get_zero_pattern(zone_dist)
        
        # 检测连续0
        consecutive_zeros = self.find_consecutive_zeros(last_pattern)
        
        if not consecutive_zeros:
            return 3.13  # 默认均值
        
        # 根据最长的连续0确定倾向
        max_length = max(length for _, length in consecutive_zeros)
        
        if max_length == 2:
            predicted_count = self.consecutive_rules['consecutive_2']['avg_next_zeros']
            print(f"  连续2个0规律: 预测下一期{predicted_count:.2f}个0分区")
        elif max_length >= 3:
            predicted_count = self.consecutive_rules['consecutive_3']['avg_next_zeros']
            print(f"  连续3个0规律: 预测下一期{predicted_count:.2f}个0分区")
        else:
            predicted_count = 3.13
        
        return predicted_count
    
    def generate_consecutive_enhanced_prediction(self, recent_data: List[Dict]) -> Dict:
        """生成集成连续0规律的增强预测"""
        # 0. 获取需要排除的模式和数量
        exclude_patterns, exclude_counts = self.get_recent_patterns_and_counts(recent_data, exclude_periods=50)
        
        # 1. 获取马尔科夫链预测作为基础
        markov_pattern, markov_confidence, markov_count = self.get_markov_prediction(recent_data)
        
        # 2. 计算自适应位置概率
        position_probabilities = self.calculate_adaptive_probabilities(recent_data)
        
        # 3. 应用连续0规律增强
        consecutive_enhanced_probabilities = self.apply_consecutive_zero_boost(
            position_probabilities, recent_data
        )
        
        # 4. 融合马尔科夫预测和连续0增强概率
        markov_boost = np.zeros(7)
        for i, char in enumerate(markov_pattern):
            if char == '0':
                markov_boost[i] = 0.3
        
        # 三重融合：50%自适应 + 30%连续0增强 + 20%马尔科夫
        final_probabilities = (position_probabilities * 0.5 + 
                             consecutive_enhanced_probabilities * 0.3 + 
                             markov_boost * 0.2)
        final_probabilities = np.clip(final_probabilities, 0.1, 0.8)
        
        # 5. 智能数量预测（融合连续0规律）
        adaptive_count = self.smart_count_prediction_with_exclusion(recent_data, exclude_counts)
        consecutive_count = self.predict_consecutive_zero_count(recent_data)
        
        # 融合数量预测：50%自适应 + 30%连续0规律 + 20%马尔科夫
        if markov_count not in exclude_counts:
            target_count = (adaptive_count * 0.5 + 
                          consecutive_count * 0.3 + 
                          markov_count * 0.2)
        else:
            target_count = (adaptive_count * 0.7 + consecutive_count * 0.3)
        
        target_count = max(2, min(5, int(round(target_count))))
        
        # 6. 概率加权位置选择（使用最终融合概率）
        max_attempts = 100
        attempt = 0
        selected_positions = None
        predicted_pattern = None
        
        while attempt < max_attempts:
            attempt += 1
            temp_selected_positions = []
            remaining_positions = list(range(7))
            remaining_probs = final_probabilities.copy()
            
            for _ in range(target_count):
                if not remaining_positions:
                    break
                
                prob_sum = sum(remaining_probs[pos] for pos in remaining_positions)
                if prob_sum > 0:
                    normalized_probs = [remaining_probs[pos] / prob_sum for pos in remaining_positions]
                    selected_idx = np.random.choice(len(remaining_positions), p=normalized_probs)
                    selected_pos = remaining_positions[selected_idx]
                    temp_selected_positions.append(selected_pos)
                    remaining_positions.remove(selected_pos)
                    
                    # 基于相关性调整
                    for remaining_pos in remaining_positions:
                        if self.position_correlations[selected_pos][remaining_pos] < -0.1:
                            remaining_probs[remaining_pos] *= 0.85
                else:
                    selected_pos = np.random.choice(remaining_positions)
                    temp_selected_positions.append(selected_pos)
                    remaining_positions.remove(selected_pos)
            
            # 生成预测模式
            temp_predicted_pattern = ['x'] * 7
            for pos in temp_selected_positions:
                temp_predicted_pattern[pos] = '0'
            temp_pattern_str = ''.join(temp_predicted_pattern)
            
            # 检查是否与最近50期重复
            if temp_pattern_str not in exclude_patterns:
                selected_positions = temp_selected_positions
                predicted_pattern = temp_predicted_pattern
                if attempt > 1:
                    print(f"  模式排除: 尝试{attempt}次找到未重复模式")
                break
        
        if selected_positions is None:
            selected_positions = temp_selected_positions
            predicted_pattern = temp_predicted_pattern
            print(f"  模式排除: {max_attempts}次尝试后仍重复，使用当前模式")
        
        # 7. 计算融合置信度
        base_confidence = np.mean([final_probabilities[pos] for pos in selected_positions])
        combined_confidence = base_confidence * 0.6 + markov_confidence * 0.4
        
        if combined_confidence > self.confidence_threshold:
            adjusted_confidence = combined_confidence * 1.1
        else:
            adjusted_confidence = combined_confidence * 0.9
        
        adjusted_confidence = np.clip(adjusted_confidence, 0.2, 0.8)
        
        return {
            'predicted_pattern': ''.join(predicted_pattern),
            'predicted_count': target_count,
            'position_probabilities': final_probabilities.tolist(),
            'confidence': adjusted_confidence,
            'selected_positions': selected_positions,
            'method': 'consecutive_enhanced_fusion_system',
            'adaptation_level': self.adaptation_count,
            'excluded_patterns_count': len(exclude_patterns),
            'excluded_counts': list(exclude_counts),
            'generation_attempts': attempt,
            'markov_pattern': markov_pattern,
            'markov_confidence': markov_confidence,
            'markov_count': markov_count,
            'consecutive_count': consecutive_count,
            'fusion_weights': {
                'adaptive': 0.5, 
                'consecutive_enhanced': 0.3, 
                'markov': 0.2
            }
        }


def run_consecutive_enhanced_test(data: List[Dict], test_periods: int = 20):
    """运行连续0增强预测测试"""
    print("=" * 80)
    print("连续0规律增强预测系统测试")
    print("=" * 80)
    
    # 分割数据
    train_data = data[:-test_periods]
    test_data = data[-test_periods:]
    
    predictor = ConsecutiveEnhancedSystem()
    
    print(f"训练数据: {len(train_data)}期")
    print(f"测试数据: {test_periods}期")
    
    # 预测测试
    print(f"\n连续0规律增强预测结果:")
    print("期号      实际模式    增强预测    马尔科夫    实际0数 预测0数 匹配度  置信度")
    print("-" * 85)
    
    total_matches = 0
    total_confidence = 0
    count_matches = 0
    
    for i, test_record in enumerate(test_data):
        # 获取历史数据
        history = train_data + test_data[:i]
        recent_history = history[-100:]
        
        # 连续0增强预测
        prediction = predictor.generate_consecutive_enhanced_prediction(recent_history)
        predicted_pattern = prediction['predicted_pattern']
        
        # 实际结果
        actual_zone_dist = predictor.parse_zone_ratio(test_record['分区比'])
        actual_pattern = predictor.get_zero_pattern(actual_zone_dist)
        
        # 计算匹配度
        matches = sum(1 for p, a in zip(predicted_pattern, actual_pattern) if p == a)
        total_matches += matches
        total_confidence += prediction['confidence']
        
        # 数量匹配
        actual_count = actual_pattern.count('0')
        predicted_count = prediction['predicted_count']
        if actual_count == predicted_count:
            count_matches += 1
        
        # 更新性能跟踪
        predictor.update_performance_tracking(actual_pattern, predicted_pattern, 
                                            actual_count, predicted_count)
        
        # 马尔科夫信息
        markov_pattern = prediction.get('markov_pattern', 'unknown')
        
        print(f"{test_record['期号']:<8} {actual_pattern:<10} {predicted_pattern:<10} "
              f"{markov_pattern:<10} {actual_count:<6} {predicted_count:<6} "
              f"{matches}/7      {prediction['confidence']:.2f}")
        
        # 检查是否需要自适应
        if predictor.should_adapt():
            predictor.auto_adapt()
            print(f"    → 触发自适应调整")
    
    # 统计结果
    avg_accuracy = total_matches / (test_periods * 7)
    avg_confidence = total_confidence / test_periods
    count_accuracy = count_matches / test_periods
    
    print(f"\n" + "=" * 80)
    print("连续0规律增强预测系统结果")
    print("=" * 80)
    
    print(f"最终性能:")
    print(f"  平均位置准确率: {avg_accuracy:.1%}")
    print(f"  数量预测准确率: {count_accuracy:.1%}")
    print(f"  平均置信度: {avg_confidence:.2f}")
    print(f"  自适应调整次数: {predictor.adaptation_count}")
    
    print(f"\n连续0规律增强特性:")
    print(f"  ✅ 连续2个0规律: 72.1%位置延续概率")
    print(f"  ✅ 连续3个0规律: 90.2%位置延续概率")
    print(f"  ✅ 三重融合预测: 50%自适应 + 30%连续0 + 20%马尔科夫")
    print(f"  ✅ 智能位置增强: 相同位置和相邻位置概率提升")
    print(f"  ✅ 数量规律融合: 基于连续0历史统计调整预测")
    
    return {
        'position_accuracy': avg_accuracy,
        'count_accuracy': count_accuracy,
        'confidence': avg_confidence,
        'adaptations': predictor.adaptation_count
    }


def main():
    """主函数"""
    try:
        # 加载数据
        db_manager = SQLiteManager()
        df = db_manager.load_results(LotteryType.DLT)
        data = df.to_dict('records')
        
        if len(data) < 100:
            print("数据不足")
            return
        
        # 运行连续0增强预测测试
        run_consecutive_enhanced_test(data, test_periods=20)
        
        print(f"\n总结:")
        print(f"连续0规律增强预测系统基于o3-mini深度分析结果，")
        print(f"成功集成了连续0位置延续和数量变化规律，")
        print(f"实现了自适应、马尔科夫和连续0规律的三重融合预测。")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
